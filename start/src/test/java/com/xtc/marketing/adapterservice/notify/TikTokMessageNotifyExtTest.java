package com.xtc.marketing.adapterservice.notify;

import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.notify.executor.extension.TikTokMessageNotifyExt;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 抖音消息通知扩展点测试
 */
@ExtendWith(MockitoExtension.class)
class TikTokMessageNotifyExtTest {

    @Mock
    private TikTokRpc tikTokRpc;

    @Mock
    private ShopGetQryExe shopGetQryExe;

    @InjectMocks
    private TikTokMessageNotifyExt tikTokMessageNotifyExt;

    private ShopDO mockShop;

    @BeforeEach
    void setUp() {
        mockShop = ShopDO.builder()
                .shopId("123456")
                .appKey("test_app_key")
                .appSecret("test_app_secret")
                .build();
    }

    @Test
    void testHandleTestMessage() {
        // 测试消息数据
        String testMessageData = "[{\"tag\":\"0\",\"msg_id\":\"0\",\"data\":\"2020-09-10T16:27:56.52842897+08:00\"}]";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(testMessageData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":0,\"msg\":\"success\"}", result.getResponseStr());
    }

    @Test
    void testHandleEmptyMessage() {
        // 空消息数据
        String emptyMessageData = "[]";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(emptyMessageData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":100002,\"msg\":\"消息数据为空\"}", result.getResponseStr());
    }

    @Test
    void testHandleUnsupportedMessage() {
        // 不支持的消息类型
        String unsupportedMessageData = "[{\"tag\":\"unsupported_message\",\"msg_id\":\"123456\",\"data\":{\"test\":\"data\"}}]";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(unsupportedMessageData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":100001,\"msg\":\"验签失败\"}", result.getResponseStr());
        assertEquals("123456", result.getDataId());
    }

    @Test
    void testHandleAddressChangeAppliedMessage() {
        // 买家收货信息变更申请消息
        String addressChangeAppliedData = "[{\"tag\":\"doudian_trade_TradeAddressChangeApplied\",\"msg_id\":\"123456789\",\"data\":{\"shop_id\":123456,\"p_id\":4836875908288530000,\"apply_time\":1630269374}}]";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(addressChangeAppliedData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":0,\"msg\":\"success\"}", result.getResponseStr());
        assertEquals("123456789", result.getDataId());
    }

    @Test
    void testHandleAddressChangedMessage() {
        // 买家收货信息变更成功消息
        String addressChangedData = "[{\"tag\":\"doudian_trade_TradeAddressChanged\",\"msg_id\":\"987654321\",\"data\":{\"shop_id\":123456,\"p_id\":4837325080261221000,\"order_status\":2}}]";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(addressChangedData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":0,\"msg\":\"success\"}", result.getResponseStr());
        assertEquals("987654321", result.getDataId());
    }

    @Test
    void testHandleInvalidJsonMessage() {
        // 无效的JSON数据
        String invalidJsonData = "invalid json data";
        
        NotifyReceiveCmd cmd = NotifyReceiveCmd.builder()
                .data(invalidJsonData)
                .build();

        ReceiveLogDO result = tikTokMessageNotifyExt.createReceiveLog(NotifyEnum.TIKTOK_SHOP_MESSAGE, cmd);

        assertNotNull(result);
        assertEquals("{\"code\":100003,\"msg\":\"系统错误\"}", result.getResponseStr());
    }

    @Test
    void testNotifyResponse() {
        String responseStr = "{\"code\":0,\"msg\":\"success\"}";
        
        var response = tikTokMessageNotifyExt.notifyResponse(responseStr);
        
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertEquals(responseStr, response.getBody());
    }

    @Test
    void testConvertToNotifyBean() {
        String testData = "{\"test\":\"data\"}";
        
        Object result = tikTokMessageNotifyExt.convertToNotifyBean(testData);
        
        assertNotNull(result);
    }
}
