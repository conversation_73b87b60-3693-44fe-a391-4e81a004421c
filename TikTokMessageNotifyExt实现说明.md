# 抖音异步消息审核方案实现说明

## 概述

本文档说明了抖音异步消息审核方案的实现，主要包括买家地址变更审核流程的消息接收和处理。

## 实现功能

### 1. 消息接收接口
- 在 `NotifyController` 中已实现 `/api/notify-receive/tiktok-message` 接口
- 支持接收抖音平台推送的消息通知

### 2. 消息验签
- 使用抖音SDK的标准验签流程
- 通过 `GlobalConfig.AddAppKeyAndAppSecret()` 设置应用密钥
- 使用 `request.checkSign()` 进行消息验签

### 3. 支持的消息类型

#### 测试消息 (tag="0")
- 直接返回成功响应 `{"code":0,"msg":"success"}`
- 用于平台验证推送地址的可用性

#### 买家收货信息变更申请消息 (doudian_trade_TradeAddressChangeApplied)
- 解析消息获取订单信息和店铺信息
- 根据审核规则自动判断是否允许地址变更
- 调用 `/order/addressConfirm` 接口进行审核回复

#### 买家收货信息变更成功消息 (doudian_trade_TradeAddressChanged)
- 接收地址变更成功通知
- 更新本地系统的订单收货地址信息

#### 其他消息类型
- 返回验签失败响应 `{"code":100001,"msg":"验签失败"}`

## 审核规则逻辑

根据抖音异步消息审核方案文档的要求：

1. **当订单已经开始拣货时，自动拒绝** - 返回代码 1001
2. **当订单进入出库环节时，自动拒绝** - 返回代码 1004  
3. **非以上环节自动通过** - 返回代码 0

### 审核结果代码说明
- `0`: 确认地址变更申请（通过）
- `1001`: 订单已进入拣货环节
- `1002`: 订单已进入配货环节
- `1003`: 订单已进入仓库环节
- `1004`: 订单已进入出库环节
- `1005`: 订单已进入发货环节

## 核心实现类

### TikTokMessageNotifyExt
位置：`adapter-service-app/src/main/java/com/xtc/marketing/adapterservice/notify/executor/extension/TikTokMessageNotifyExt.java`

主要方法：
- `createReceiveLog()`: 处理消息接收的主入口
- `handleAddressChangeApplied()`: 处理地址变更申请消息
- `handleAddressChanged()`: 处理地址变更成功消息
- `determineAuditResult()`: 确定审核结果
- `callAddressConfirm()`: 调用审核接口
- `updateLocalOrderAddress()`: 更新本地订单地址

## 消息处理流程

```
1. 接收消息 -> 解析JSON数组
2. 检查是否为测试消息 -> 如果是，直接返回成功
3. 遍历消息数组，根据tag处理不同类型消息
4. 对于地址变更申请消息：
   - 使用抖音SDK解析消息
   - 获取店铺信息并验签
   - 根据订单状态确定审核结果
   - 调用addressConfirm接口
5. 对于地址变更成功消息：
   - 使用抖音SDK解析消息
   - 获取店铺信息并验签
   - 更新本地订单地址信息
6. 返回处理结果
```

## 错误处理

- `100001`: 验签失败
- `100002`: 消息数据为空
- `100003`: 系统错误

## 测试

已创建测试用例 `TikTokMessageNotifyExtTest`，覆盖以下场景：
- 测试消息处理
- 空消息处理
- 不支持消息类型处理
- 地址变更申请消息处理
- 地址变更成功消息处理
- 无效JSON数据处理

## 注意事项

1. **超时处理**: 抖音平台要求在15分钟内回复审核结果，否则默认失败
2. **幂等处理**: 消息可能重复推送，需要做好幂等处理
3. **订单状态查询**: 当前审核逻辑为默认通过，实际使用时需要集成OMS系统查询订单状态
4. **地址更新**: 当前只是记录日志，实际使用时需要调用相应的业务接口更新订单地址

## 配置要求

1. 在抖音开放平台配置消息推送地址
2. 订阅相关消息类型：
   - `doudian_trade_TradeAddressChangeApplied`
   - `doudian_trade_TradeAddressChanged`
3. 开启消息推送服务
4. 确保店铺信息在系统中正确配置（appKey、appSecret等）
