# 抖音消息通知扩展点 - 函数式编程架构实现说明

## 概述

本文档说明了使用函数式编程方式重构的抖音消息通知扩展点实现，采用 lambda 函数和函数式接口来实现更优雅、可扩展的消息处理架构。

## 架构设计

### 1. 核心设计原则

- **职责分离**: 主逻辑负责流程控制，扩展函数负责具体业务处理
- **函数式编程**: 使用 lambda 表达式和函数式接口实现扩展点
- **可扩展性**: 通过 Map 映射轻松添加新的消息类型处理
- **统一验签**: 在主逻辑中统一处理店铺获取和验签逻辑

### 2. 架构组件

#### 2.1 消息转换器映射 (Data Converters)
```java
private final Map<String, Function<String, DoudianOpMsgRequest<?>>> messageConverters = Map.of(
    "doudian_trade_TradeAddressChangeApplied", this::convertToAddressChangeAppliedRequest,
    "doudian_trade_TradeAddressChanged", this::convertToAddressChangedRequest
);
```

#### 2.2 消息处理器映射 (Message Processors)
```java
private final Map<String, Consumer<MessageProcessContext>> messageProcessors = Map.of(
    "doudian_trade_TradeAddressChangeApplied", this::processAddressChangeApplied,
    "doudian_trade_TradeAddressChanged", this::processAddressChanged
);
```

#### 2.3 消息处理上下文 (Message Process Context)
```java
private static class MessageProcessContext {
    private final String tag;
    private final String rawData;
    private final ShopDO shop;
    private final DoudianOpMsgRequest<?> request;
    private final Object messageData;
}
```

## 处理流程

### 主流程 (createReceiveLog)

1. **消息解析**: 解析 JSON 数组格式的消息数据
2. **测试消息检查**: 特殊处理 tag="0" 的测试消息
3. **消息类型验证**: 检查是否支持该消息类型
4. **委托处理**: 调用 `processMessage` 进行具体处理

### 核心处理逻辑 (processMessage)

```java
private Optional<String> processMessage(String tag, String rawData) {
    // 1. 数据转换扩展 - 使用lambda函数
    Function<String, DoudianOpMsgRequest<?>> converter = messageConverters.get(tag);
    DoudianOpMsgRequest<?> request = converter.apply(rawData);

    // 2. 获取店铺信息（在主逻辑中）
    ShopDO shop = extractShopFromRequest(request);
    
    // 3. 主逻辑使用数据转换提供的request进行验签
    GlobalConfig.AddAppKeyAndAppSecret(shop.getAppKey(), shop.getAppSecret());
    request.checkSign();

    // 4. 验签成功后的逻辑扩展 - 使用lambda函数
    Object messageData = extractMessageDataFromRequest(request);
    MessageProcessContext context = new MessageProcessContext(tag, rawData, shop, request, messageData);
    
    Consumer<MessageProcessContext> processor = messageProcessors.get(tag);
    processor.accept(context);
}
```

## 扩展函数实现

### 1. 数据转换扩展 (Lambda Functions)

#### 地址变更申请消息转换
```java
private DoudianOpMsgRequest<?> convertToAddressChangeAppliedRequest(String rawData) {
    TradeTradeAddressChangeAppliedRequest request = new TradeTradeAddressChangeAppliedRequest();
    request.getParam().setRequestBody(rawData);
    return request;
}
```

#### 地址变更成功消息转换
```java
private DoudianOpMsgRequest<?> convertToAddressChangedRequest(String rawData) {
    TradeTradeAddressChangedRequest request = new TradeTradeAddressChangedRequest();
    request.getParam().setRequestBody(rawData);
    return request;
}
```

### 2. 验签成功后的处理扩展 (Lambda Functions)

#### 地址变更申请处理
```java
private void processAddressChangeApplied(MessageProcessContext context) {
    TradeTradeAddressChangeAppliedParam param = (TradeTradeAddressChangeAppliedParam) context.getMessageData();
    
    // 实现审核逻辑
    int auditResult = determineAuditResult(String.valueOf(param.getPId()));
    
    // 调用addressConfirm接口进行审核
    callAddressConfirm(context.getShop(), String.valueOf(param.getPId()), auditResult);
}
```

#### 地址变更成功处理
```java
private void processAddressChanged(MessageProcessContext context) {
    TradeTradeAddressChangedParam param = (TradeTradeAddressChangedParam) context.getMessageData();
    
    // 更新本地订单地址信息
    updateLocalOrderAddress(String.valueOf(param.getPId()), param.getReceiverMsg());
}
```

## 优势特点

### 1. 高度可扩展
- 添加新消息类型只需在两个 Map 中添加对应的 lambda 函数
- 无需修改主流程代码
- 支持热插拔式的消息处理器

### 2. 职责清晰
- **主逻辑**: 负责流程控制、店铺获取、验签
- **转换扩展**: 负责消息数据转换
- **处理扩展**: 负责具体业务逻辑

### 3. 统一验签
- 所有消息类型使用统一的验签流程
- 在主逻辑中集中处理店铺信息获取
- 避免重复的验签代码

### 4. 函数式编程
- 使用 `Function<String, DoudianOpMsgRequest<?>>` 进行数据转换
- 使用 `Consumer<MessageProcessContext>` 进行业务处理
- 代码更简洁、可读性更强

## 错误处理

- **数据转换异常**: 返回验签失败 (100001)
- **店铺不存在**: 返回验签失败 (100001)
- **验签失败**: 返回验签失败 (100001)
- **业务处理异常**: 记录日志但不影响响应

## 扩展示例

### 添加新消息类型

1. **添加转换器**:
```java
private final Map<String, Function<String, DoudianOpMsgRequest<?>>> messageConverters = Map.of(
    // 现有转换器...
    "new_message_type", this::convertToNewMessageRequest
);
```

2. **添加处理器**:
```java
private final Map<String, Consumer<MessageProcessContext>> messageProcessors = Map.of(
    // 现有处理器...
    "new_message_type", this::processNewMessage
);
```

3. **实现转换函数**:
```java
private DoudianOpMsgRequest<?> convertToNewMessageRequest(String rawData) {
    // 实现转换逻辑
}
```

4. **实现处理函数**:
```java
private void processNewMessage(MessageProcessContext context) {
    // 实现业务逻辑
}
```

## 测试策略

- 单元测试覆盖各个 lambda 函数
- 集成测试验证完整流程
- Mock 外部依赖进行隔离测试
- 验证错误处理和边界情况

## 总结

通过函数式编程架构重构，实现了：
- ✅ 店铺信息在主逻辑中获取
- ✅ 数据转换使用 lambda 函数扩展
- ✅ 主逻辑统一进行验签
- ✅ 验签成功后的逻辑使用 lambda 函数扩展
- ✅ 高度可扩展和可维护的架构设计
