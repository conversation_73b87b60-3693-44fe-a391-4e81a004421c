package com.xtc.marketing.adapterservice.notify.executor.extension;

import com.alibaba.cola.extension.Extension;
import com.doudian.open.core.GlobalConfig;
import com.doudian.open.core.msg.DoudianOpMsgParamRecord;
import com.doudian.open.core.msg.DoudianOpMsgRequest;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.TradeTradeAddressChangeAppliedRequest;
import com.doudian.open.msg.trade_TradeAddressChangeApplied.param.TradeTradeAddressChangeAppliedParam;
import com.doudian.open.msg.trade_TradeAddressChanged.TradeTradeAddressChangedRequest;
import com.doudian.open.msg.trade_TradeAddressChanged.param.TradeTradeAddressChangedParam;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.xtc.marketing.adapterservice.notify.dataobject.ReceiveLogDO;
import com.xtc.marketing.adapterservice.notify.dto.command.NotifyReceiveCmd;
import com.xtc.marketing.adapterservice.notify.enums.NotifyEnum;
import com.xtc.marketing.adapterservice.rpc.tiktok.TikTokRpc;
import com.xtc.marketing.adapterservice.shop.dataobject.ShopDO;
import com.xtc.marketing.adapterservice.shop.executor.query.ShopGetQryExe;
import com.xtc.marketing.adapterservice.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * 通知扩展点 - 抖音消息通知
 */
@Slf4j
@RequiredArgsConstructor
@Extension(bizId = NotifyExtConstant.BIZ_ID_TIKTOK,
        useCase = NotifyExtConstant.USE_CASE_SHOP, scenario = NotifyExtConstant.SCENARIO_MESSAGE)
public class TikTokMessageNotifyExt implements NotifyExtPt {

    private final TikTokRpc tikTokRpc;
    private final ShopGetQryExe shopGetQryExe;

    /**
     * 消息处理器映射
     */
    private final Map<String, Function<String, DoudianOpMsgRequest<?>>> messageConverters = Map.of(
            "doudian_trade_TradeAddressChangeApplied", this::convertToAddressChangeAppliedRequest,
            "doudian_trade_TradeAddressChanged", this::convertToAddressChangedRequest
    );

    /**
     * 验签成功后的处理器映射
     */
    private final Map<String, Consumer<MessageProcessContext>> messageProcessors = Map.of(
            "doudian_trade_TradeAddressChangeApplied", this::processAddressChangeApplied,
            "doudian_trade_TradeAddressChanged", this::processAddressChanged
    );

    @Override
    public ReceiveLogDO createReceiveLog(NotifyEnum notify, NotifyReceiveCmd cmd) {
        String responseStr;
        String dataId = null;
        String rawData = cmd.getData();

        try {
            // 解析消息数据
            JsonArray messageArray = GsonUtil.jsonToBean(rawData, JsonArray.class);
            if (messageArray == null || messageArray.size() == 0) {
                log.warn("抖音消息数据为空");
                responseStr = buildResponse(100002, "消息数据为空");
                return ReceiveLogDO.builder().responseStr(responseStr).build();
            }

            // 检查是否为测试消息
            JsonObject firstMessage = messageArray.get(0).getAsJsonObject();
            String firstTag = firstMessage.get("tag").getAsString();

            if ("0".equals(firstTag)) {
                // 测试消息，直接返回成功
                log.info("收到抖音测试消息");
                responseStr = buildSuccessResponse();
                return ReceiveLogDO.builder().responseStr(responseStr).build();
            }

            // 处理消息
            for (JsonElement element : messageArray) {
                JsonObject messageObj = element.getAsJsonObject();
                String tag = messageObj.get("tag").getAsString();
                String msgId = messageObj.get("msg_id").getAsString();

                dataId = msgId;

                // 检查是否支持该消息类型
                if (!messageConverters.containsKey(tag)) {
                    log.info("收到未支持的消息类型: {}", tag);
                    responseStr = buildResponse(100001, "验签失败");
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }

                // 处理支持的消息类型
                Optional<String> processResult = processMessage(tag, rawData);
                if (processResult.isPresent()) {
                    responseStr = processResult.get();
                    return ReceiveLogDO.builder().responseStr(responseStr).build();
                }
            }

            // 所有消息处理成功
            responseStr = buildSuccessResponse();

        } catch (Exception e) {
            log.error("抖音消息处理异常", e);
            responseStr = buildResponse(100003, "系统错误");
        }

        return ReceiveLogDO.builder()
                .dataId(dataId)
                .rawData(rawData)
                .responseStr(responseStr)
                .build();
    }

    /**
     * 消息处理上下文
     */
    private static class MessageProcessContext {
        private final String tag;
        private final String rawData;
        private final ShopDO shop;
        private final DoudianOpMsgRequest<?> request;
        private final Object messageData;

        public MessageProcessContext(String tag, String rawData, ShopDO shop,
                                   DoudianOpMsgRequest<?> request, Object messageData) {
            this.tag = tag;
            this.rawData = rawData;
            this.shop = shop;
            this.request = request;
            this.messageData = messageData;
        }

        public String getTag() { return tag; }
        public String getRawData() { return rawData; }
        public ShopDO getShop() { return shop; }
        public DoudianOpMsgRequest<?> getRequest() { return request; }
        public Object getMessageData() { return messageData; }
    }

    /**
     * 处理消息的核心逻辑
     */
    private Optional<String> processMessage(String tag, String rawData) {
        try {
            // 1. 数据转换扩展 - 使用lambda函数
            Function<String, DoudianOpMsgRequest<?>> converter = messageConverters.get(tag);
            DoudianOpMsgRequest<?> request = converter.apply(rawData);

            // 2. 获取店铺信息（在主逻辑中）
            ShopDO shop = extractShopFromRequest(request);
            if (shop == null) {
                log.error("店铺不存在");
                return Optional.of(buildResponse(100001, "验签失败"));
            }

            // 3. 主逻辑使用数据转换提供的request进行验签
            GlobalConfig.AddAppKeyAndAppSecret(shop.getAppKey(), shop.getAppSecret());
            request.checkSign();

            // 4. 验签成功后的逻辑扩展 - 使用lambda函数
            Object messageData = extractMessageDataFromRequest(request);
            MessageProcessContext context = new MessageProcessContext(tag, rawData, shop, request, messageData);

            Consumer<MessageProcessContext> processor = messageProcessors.get(tag);
            processor.accept(context);

            return Optional.empty(); // 处理成功，继续后续流程

        } catch (Exception e) {
            log.error("处理消息异常: tag={}", tag, e);
            return Optional.of(buildResponse(100001, "验签失败"));
        }
    }

    @Override
    public ResponseEntity<String> notifyResponse(String responseStr) {
        return ResponseEntity.ok().body(responseStr);
    }

    @Override
    public Object convertToNotifyBean(String data) {
        return GsonUtil.jsonToObject(data);
    }


    /**
     * 数据转换扩展 - 地址变更申请消息
     */
    private DoudianOpMsgRequest<?> convertToAddressChangeAppliedRequest(String rawData) {
        TradeTradeAddressChangeAppliedRequest request = new TradeTradeAddressChangeAppliedRequest();
        request.getParam().setRequestBody(rawData);
        return request;
    }

    /**
     * 数据转换扩展 - 地址变更成功消息
     */
    private DoudianOpMsgRequest<?> convertToAddressChangedRequest(String rawData) {
        TradeTradeAddressChangedRequest request = new TradeTradeAddressChangedRequest();
        request.getParam().setRequestBody(rawData);
        return request;
    }

    /**
     * 从请求中提取店铺信息
     */
    private ShopDO extractShopFromRequest(DoudianOpMsgRequest<?> request) {
        try {
            if (request instanceof TradeTradeAddressChangeAppliedRequest) {
                TradeTradeAddressChangeAppliedRequest appliedRequest = (TradeTradeAddressChangeAppliedRequest) request;
                List<DoudianOpMsgParamRecord<TradeTradeAddressChangeAppliedParam>> requestBody = appliedRequest.getRequestBody();
                if (requestBody != null && !requestBody.isEmpty()) {
                    Long shopId = requestBody.get(0).getData().getShopId();
                    return shopGetQryExe.getByShopId(shopId.toString());
                }
            } else if (request instanceof TradeTradeAddressChangedRequest) {
                TradeTradeAddressChangedRequest changedRequest = (TradeTradeAddressChangedRequest) request;
                List<DoudianOpMsgParamRecord<TradeTradeAddressChangedParam>> requestBody = changedRequest.getRequestBody();
                if (requestBody != null && !requestBody.isEmpty()) {
                    Long shopId = requestBody.get(0).getData().getShopId();
                    return shopGetQryExe.getByShopId(shopId.toString());
                }
            }
        } catch (Exception e) {
            log.error("提取店铺信息异常", e);
        }
        return null;
    }

    /**
     * 从请求中提取消息数据
     */
    private Object extractMessageDataFromRequest(DoudianOpMsgRequest<?> request) {
        try {
            if (request instanceof TradeTradeAddressChangeAppliedRequest) {
                TradeTradeAddressChangeAppliedRequest appliedRequest = (TradeTradeAddressChangeAppliedRequest) request;
                List<DoudianOpMsgParamRecord<TradeTradeAddressChangeAppliedParam>> requestBody = appliedRequest.getRequestBody();
                return requestBody != null && !requestBody.isEmpty() ? requestBody.get(0).getData() : null;
            } else if (request instanceof TradeTradeAddressChangedRequest) {
                TradeTradeAddressChangedRequest changedRequest = (TradeTradeAddressChangedRequest) request;
                List<DoudianOpMsgParamRecord<TradeTradeAddressChangedParam>> requestBody = changedRequest.getRequestBody();
                return requestBody != null && !requestBody.isEmpty() ? requestBody.get(0).getData() : null;
            }
        } catch (Exception e) {
            log.error("提取消息数据异常", e);
        }
        return null;
    }

    /**
     * 验签成功后的处理逻辑扩展 - 地址变更申请消息
     */
    private void processAddressChangeApplied(MessageProcessContext context) {
        log.info("处理买家收货信息变更申请消息");

        try {
            TradeTradeAddressChangeAppliedParam param = (TradeTradeAddressChangeAppliedParam) context.getMessageData();
            if (param == null) {
                log.error("买家收货信息变更申请消息数据为空");
                return;
            }

            // 实现审核逻辑
            // 1. 当订单已经开始拣货时，自动拒绝；
            // 2. 当订单进入出库环节时，自动拒绝；
            // 3. 非以上环节自动通过
            int auditResult = determineAuditResult(String.valueOf(param.getPId()));

            // 调用addressConfirm接口进行审核
            callAddressConfirm(context.getShop(), String.valueOf(param.getPId()), auditResult);

        } catch (Exception e) {
            log.error("处理买家收货信息变更申请消息异常", e);
        }
    }

    /**
     * 验签成功后的处理逻辑扩展 - 地址变更成功消息
     */
    private void processAddressChanged(MessageProcessContext context) {
        log.info("处理买家收货信息变更成功消息");

        try {
            TradeTradeAddressChangedParam param = (TradeTradeAddressChangedParam) context.getMessageData();
            if (param == null) {
                log.error("买家收货信息变更成功消息数据为空");
                return;
            }

            // 更新本地订单地址信息
            updateLocalOrderAddress(String.valueOf(param.getPId()), param.getReceiverMsg());

        } catch (Exception e) {
            log.error("处理买家收货信息变更成功消息异常", e);
        }
    }

    /**
     * 确定审核结果
     * 根据抖音异步消息审核方案文档的审核规则逻辑：
     * 1. 当订单已经开始拣货时，自动拒绝；
     * 2. 当订单进入出库环节时，自动拒绝；
     * 3. 非以上环节自动通过
     */
    private int determineAuditResult(String orderId) {
        try {
            // TODO: 这里需要查询OMS系统获取订单状态
            // 根据订单状态判断是否允许修改地址

            // 审核结果代码说明：
            // 0: 确认地址变更申请（通过）
            // 1001: 订单已进入拣货环节
            // 1002: 订单已进入配货环节
            // 1003: 订单已进入仓库环节
            // 1004: 订单已进入出库环节
            // 1005: 订单已进入发货环节

            // 示例逻辑（实际需要根据OMS系统的订单状态进行判断）
            // String orderStatus = omsRpc.getOrderStatus(orderId);
            // if ("PICKING".equals(orderStatus)) {
            //     return 1001; // 订单已进入拣货环节
            // } else if ("OUTBOUND".equals(orderStatus)) {
            //     return 1004; // 订单已进入出库环节
            // }

            // 默认通过（实际项目中需要根据具体的订单状态判断）
            log.info("订单 {} 地址变更审核默认通过", orderId);
            return 0;

        } catch (Exception e) {
            log.error("确定审核结果异常，订单ID: {}", orderId, e);
            // 异常情况下默认拒绝，使用通用拒绝原因
            return 1003;
        }
    }

    /**
     * 调用addressConfirm接口审核
     */
    private void callAddressConfirm(ShopDO shop, String orderId, int result) {
        try {
            boolean success = tikTokRpc.addressConfirm(shop, orderId, result);
            if (success) {
                log.info("地址变更审核成功: 订单={}, 结果={}", orderId, result);
            } else {
                log.warn("地址变更审核失败: 订单={}, 结果={}", orderId, result);
            }
        } catch (Exception e) {
            log.error("调用addressConfirm接口异常: 订单={}, 结果={}", orderId, result, e);
        }
    }

    /**
     * 更新本地订单地址
     */
    private void updateLocalOrderAddress(String orderId, Object receiverMsg) {
        try {
            // TODO: 更新本地系统的订单收货地址
            // 这里可以调用OMS系统接口更新订单地址信息
            log.info("更新订单 {} 收货地址: {}", orderId, GsonUtil.objectToJson(receiverMsg));
        } catch (Exception e) {
            log.error("更新本地订单地址异常", e);
        }
    }

    /**
     * 构建成功响应
     */
    private String buildSuccessResponse() {
        return buildResponse(0, "success");
    }

    /**
     * 构建响应
     */
    private String buildResponse(int code, String message) {
        JsonObject result = new JsonObject();
        result.addProperty("code", code);
        result.addProperty("msg", message);
        return result.toString();
    }
}